#!/usr/bin/env python3
"""
模拟实际应用启动过程的测试脚本
"""
import asyncio
import logging
import sys
import os
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_full_startup_sequence():
    """测试完整的启动序列"""
    print("🚀 测试完整启动序列...")
    
    try:
        # 1. 测试配置加载
        print("\n1️⃣ 测试配置加载...")
        from app.core.config import settings
        print(f"   ✅ 配置加载成功")
        print(f"   - REDIS_ENABLED: {getattr(settings, 'REDIS_ENABLED', 'NOT_SET')}")
        
        # 2. 测试startup模块导入
        print("\n2️⃣ 测试startup模块导入...")
        from app.core.startup import startup_sequence
        print("   ✅ startup模块导入成功")
        
        # 3. 执行启动序列
        print("\n3️⃣ 执行启动序列...")
        success = await startup_sequence()
        
        if success:
            print("   ✅ 启动序列执行成功")
        else:
            print("   ⚠️ 启动序列部分失败")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 启动序列执行失败: {e}")
        traceback.print_exc()
        return False

async def test_individual_components():
    """测试各个组件的初始化"""
    print("\n🔧 测试各个组件初始化...")
    
    components = [
        ("Neo4j连接池", "app.core.startup", "initialize_neo4j_pool"),
        ("Redis缓存服务", "app.core.startup", "initialize_redis_cache"),
        ("增强缓存服务", "app.core.startup", "initialize_enhanced_cache"),
        ("混合检索引擎", "app.core.startup", "initialize_hybrid_retrieval"),
    ]
    
    results = {}
    
    for name, module_name, func_name in components:
        print(f"\n🔍 测试 {name}...")
        try:
            # 动态导入模块和函数
            module = __import__(module_name, fromlist=[func_name])
            func = getattr(module, func_name)
            
            # 执行初始化函数
            result = await func()
            results[name] = result
            
            if result:
                print(f"   ✅ {name} 初始化成功")
            else:
                print(f"   ⚠️ {name} 初始化失败")
                
        except Exception as e:
            print(f"   ❌ {name} 初始化异常: {e}")
            results[name] = False
            # 打印详细错误信息
            traceback.print_exc()
    
    return results

async def test_redis_specific():
    """专门测试Redis相关的问题"""
    print("\n🔍 专门测试Redis相关问题...")
    
    try:
        # 1. 测试Redis模块导入
        print("1. 测试Redis模块导入...")
        import redis.asyncio as redis
        from redis.exceptions import ConnectionError, TimeoutError
        print("   ✅ Redis模块导入成功")
        
        # 2. 测试Redis缓存服务导入
        print("2. 测试Redis缓存服务导入...")
        from app.services.redis_cache_service import redis_cache, RedisCacheService
        print("   ✅ Redis缓存服务导入成功")
        
        # 3. 测试Redis缓存服务初始化
        print("3. 测试Redis缓存服务初始化...")
        await redis_cache.initialize()
        print(f"   ✅ Redis缓存服务初始化完成，状态: {redis_cache._initialized}")
        
        # 4. 测试Redis配置
        print("4. 测试Redis配置...")
        from app.core.config import settings
        print(f"   - REDIS_ENABLED: {settings.REDIS_ENABLED}")
        print(f"   - REDIS_HOST: {settings.REDIS_HOST}")
        print(f"   - REDIS_PORT: {settings.REDIS_PORT}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Redis测试失败: {e}")
        traceback.print_exc()
        return False

def capture_startup_logs():
    """捕获启动日志"""
    print("\n📋 捕获启动日志...")
    
    # 创建一个日志处理器来捕获日志
    import io
    log_stream = io.StringIO()
    handler = logging.StreamHandler(log_stream)
    handler.setLevel(logging.WARNING)
    
    # 添加到根日志记录器
    root_logger = logging.getLogger()
    root_logger.addHandler(handler)
    
    try:
        # 执行可能产生错误日志的操作
        from app.core.startup import initialize_redis_cache
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(initialize_redis_cache())
        loop.close()
        
        # 获取日志内容
        log_content = log_stream.getvalue()
        if log_content:
            print("   📝 捕获到的警告/错误日志:")
            print(log_content)
        else:
            print("   ✅ 没有警告或错误日志")
            
    except Exception as e:
        print(f"   ❌ 日志捕获失败: {e}")
    finally:
        root_logger.removeHandler(handler)
        handler.close()

async def main():
    """主函数"""
    print("🚀 实际启动过程测试工具")
    print("=" * 60)
    
    # 1. 测试Redis专门问题
    redis_result = await test_redis_specific()
    
    # 2. 测试各个组件
    component_results = await test_individual_components()
    
    # 3. 测试完整启动序列
    startup_result = await test_full_startup_sequence()
    
    # 4. 捕获启动日志
    capture_startup_logs()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"Redis专项测试: {'✅ 成功' if redis_result else '❌ 失败'}")
    
    print("组件测试结果:")
    for name, result in component_results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  - {name}: {status}")
    
    print(f"完整启动测试: {'✅ 成功' if startup_result else '❌ 失败'}")
    
    # 分析问题
    if not redis_result:
        print("\n🔍 Redis问题分析:")
        print("- 检查Redis包是否正确安装")
        print("- 检查.env文件中的REDIS_ENABLED设置")
        print("- 检查Redis服务器是否运行")
    
    failed_components = [name for name, result in component_results.items() if not result]
    if failed_components:
        print(f"\n⚠️ 失败的组件: {', '.join(failed_components)}")
        print("建议检查相关服务的配置和依赖")

if __name__ == "__main__":
    asyncio.run(main())
