#!/usr/bin/env python3
"""
测试各个模块的导入
"""
import sys
import os
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_individual_imports():
    """测试各个端点模块的导入"""
    modules_to_test = [
        "app.api.api_v1.endpoints.connections",
        "app.api.api_v1.endpoints.schema", 
        "app.api.api_v1.endpoints.query",
        "app.api.api_v1.endpoints.value_mappings",
        "app.api.api_v1.endpoints.text2sql",
        "app.api.api_v1.endpoints.text2sql_sse",
        "app.api.api_v1.endpoints.graph_visualization",
        "app.api.api_v1.endpoints.relationship_tips",
        "app.api.api_v1.endpoints.hybrid_qa",
        "app.api.api_v1.endpoints.chat_history"
    ]
    
    print("🔍 测试各个端点模块导入...")
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}: 导入成功")
        except Exception as e:
            print(f"❌ {module_name}: 导入失败")
            print(f"   错误: {str(e)}")
            traceback.print_exc()
            print("-" * 50)

def test_api_router_import():
    """测试API路由导入"""
    print("\n🔍 测试API路由导入...")
    
    try:
        from app.api.api_v1.api import api_router
        print("✅ API路由导入成功")
        return True
    except Exception as e:
        print(f"❌ API路由导入失败: {str(e)}")
        traceback.print_exc()
        return False

def test_main_import():
    """测试main模块导入"""
    print("\n🔍 测试main模块导入...")
    
    try:
        import main
        print("✅ main模块导入成功")
        return True
    except Exception as e:
        print(f"❌ main模块导入失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 模块导入测试工具")
    print("=" * 50)
    
    # 测试各个端点模块
    test_individual_imports()
    
    # 测试API路由
    api_success = test_api_router_import()
    
    # 测试main模块
    main_success = test_main_import()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"API路由导入: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"Main模块导入: {'✅ 成功' if main_success else '❌ 失败'}")
    
    if api_success and main_success:
        print("🎉 所有关键模块导入成功！")
    else:
        print("⚠️ 部分模块导入失败，请检查上述错误信息。")

if __name__ == "__main__":
    main()
