#!/usr/bin/env python3
"""
最终架构验证脚本
验证数据库架构优化和冗余清理的最终结果
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path

class FinalArchitectureVerifier:
    """最终架构验证器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.resource_db = "resource.db"
        self.business_db = "fin_data.db"
        
        # 期望的最终架构
        self.expected_architecture = {
            'resource_db': {
                'system_core_tables': [
                    'dbconnection', 'chatsession', 'chatmessage', 'chathistorysnapshot'
                ],
                'database_metadata_tables': [
                    'schematable', 'schemacolumn', 'schemarelationship', 'valuemapping'
                ],
                'ai_enhanced_metadata_tables': [
                    'meta_table_descriptions', 'meta_column_descriptions', 'meta_business_rules'
                ],
                'system_ai_tables': [
                    'ai_prompt_templates', 'query_patterns'
                ]
            },
            'business_db': {
                'core_business_data': [
                    'financial_data'
                ],
                'business_metadata_tables': [
                    'field_relationships', 'data_quality_rules'
                ]
            }
        }
        
        self.verification_results = {
            'timestamp': self.timestamp,
            'architecture_verification': {},
            'redundancy_check': {},
            'data_integrity_check': {},
            'overall_status': 'UNKNOWN'
        }
    
    def verify_architecture(self):
        """验证最终架构"""
        print("🔍 开始最终架构验证...")
        print("=" * 60)
        
        try:
            # 1. 验证数据库表结构
            self._verify_database_structure()
            
            # 2. 检查冗余情况
            self._check_redundancy_elimination()
            
            # 3. 验证数据完整性
            self._verify_data_integrity()
            
            # 4. 生成最终报告
            self._generate_final_report()
            
            print(f"\n✅ 最终架构验证完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 验证过程中出现错误: {e}")
            return False
    
    def _verify_database_structure(self):
        """验证数据库表结构"""
        print(f"\n📊 验证数据库表结构...")
        
        # 验证resource.db
        resource_result = self._verify_single_database(
            self.resource_db, 
            self.expected_architecture['resource_db']
        )
        self.verification_results['architecture_verification']['resource_db'] = resource_result
        
        # 验证fin_data.db
        business_result = self._verify_single_database(
            self.business_db,
            self.expected_architecture['business_db']
        )
        self.verification_results['architecture_verification']['business_db'] = business_result
        
        # 打印结果
        print(f"  📊 resource.db: {'✅' if resource_result['compliant'] else '❌'}")
        print(f"  📊 fin_data.db: {'✅' if business_result['compliant'] else '❌'}")
    
    def _verify_single_database(self, db_file, expected_structure):
        """验证单个数据库结构"""
        try:
            if not Path(db_file).exists():
                return {
                    'compliant': False,
                    'error': f'数据库文件不存在: {db_file}'
                }
            
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            actual_tables = set(row[0] for row in cursor.fetchall())
            
            # 收集所有期望的表
            expected_tables = set()
            for category, tables in expected_structure.items():
                expected_tables.update(tables)
            
            # 检查合规性
            missing_tables = expected_tables - actual_tables
            extra_tables = actual_tables - expected_tables
            
            compliant = len(missing_tables) == 0 and len(extra_tables) == 0
            
            result = {
                'compliant': compliant,
                'actual_tables': list(actual_tables),
                'expected_tables': list(expected_tables),
                'missing_tables': list(missing_tables),
                'extra_tables': list(extra_tables),
                'table_count': len(actual_tables)
            }
            
            # 按类别统计
            category_stats = {}
            for category, tables in expected_structure.items():
                found_tables = [t for t in tables if t in actual_tables]
                category_stats[category] = {
                    'expected': len(tables),
                    'found': len(found_tables),
                    'complete': len(found_tables) == len(tables)
                }
            
            result['category_stats'] = category_stats
            
            conn.close()
            return result
            
        except Exception as e:
            return {
                'compliant': False,
                'error': str(e)
            }
    
    def _check_redundancy_elimination(self):
        """检查冗余消除情况"""
        print(f"\n🔍 检查冗余消除情况...")
        
        # 检查是否还有重复表名
        resource_tables = self._get_table_names(self.resource_db)
        business_tables = self._get_table_names(self.business_db)
        
        duplicate_tables = resource_tables & business_tables
        
        # 检查特定的冗余表是否已删除
        removed_tables = ['biz_business_rules', 'biz_column_descriptions', 'biz_table_descriptions']
        still_exists = [t for t in removed_tables if t in business_tables]
        
        # 检查迁移的表是否在正确位置
        migrated_tables = ['ai_prompt_templates', 'query_patterns']
        correctly_migrated = [t for t in migrated_tables if t in resource_tables and t not in business_tables]
        
        redundancy_result = {
            'no_duplicates': len(duplicate_tables) == 0,
            'duplicate_tables': list(duplicate_tables),
            'redundant_tables_removed': len(still_exists) == 0,
            'still_existing_redundant': still_exists,
            'tables_correctly_migrated': len(correctly_migrated) == len(migrated_tables),
            'migrated_tables': correctly_migrated
        }
        
        self.verification_results['redundancy_check'] = redundancy_result
        
        print(f"  🔄 重复表名: {'✅ 无' if redundancy_result['no_duplicates'] else f'❌ {len(duplicate_tables)}个'}")
        print(f"  🗑️ 冗余表删除: {'✅ 完成' if redundancy_result['redundant_tables_removed'] else '❌ 未完成'}")
        print(f"  📦 表迁移: {'✅ 完成' if redundancy_result['tables_correctly_migrated'] else '❌ 未完成'}")
    
    def _get_table_names(self, db_file):
        """获取数据库表名"""
        if not Path(db_file).exists():
            return set()
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = set(row[0] for row in cursor.fetchall())
        conn.close()
        return tables
    
    def _verify_data_integrity(self):
        """验证数据完整性"""
        print(f"\n🔍 验证数据完整性...")
        
        integrity_checks = []
        
        # 检查核心业务数据
        financial_data_check = self._check_table_data(self.business_db, 'financial_data')
        integrity_checks.append({
            'table': 'financial_data',
            'database': self.business_db,
            'status': 'OK' if financial_data_check['exists'] and financial_data_check['record_count'] > 0 else 'ERROR',
            'record_count': financial_data_check.get('record_count', 0)
        })
        
        # 检查元数据表
        meta_tables = ['meta_table_descriptions', 'meta_column_descriptions', 'meta_business_rules']
        for table in meta_tables:
            table_check = self._check_table_data(self.resource_db, table)
            integrity_checks.append({
                'table': table,
                'database': self.resource_db,
                'status': 'OK' if table_check['exists'] else 'ERROR',
                'record_count': table_check.get('record_count', 0)
            })
        
        # 检查迁移的表
        migrated_tables = ['ai_prompt_templates', 'query_patterns']
        for table in migrated_tables:
            table_check = self._check_table_data(self.resource_db, table)
            integrity_checks.append({
                'table': table,
                'database': self.resource_db,
                'status': 'OK' if table_check['exists'] else 'ERROR',
                'record_count': table_check.get('record_count', 0)
            })
        
        self.verification_results['data_integrity_check'] = {
            'checks': integrity_checks,
            'all_passed': all(check['status'] == 'OK' for check in integrity_checks)
        }
        
        # 打印结果
        for check in integrity_checks:
            status_icon = "✅" if check['status'] == 'OK' else "❌"
            print(f"  {status_icon} {check['table']}: {check['record_count']} 条记录")
    
    def _check_table_data(self, db_file, table_name):
        """检查表数据"""
        try:
            if not Path(db_file).exists():
                return {'exists': False}
            
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                return {'exists': False}
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            record_count = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'exists': True,
                'record_count': record_count
            }
            
        except Exception as e:
            return {
                'exists': False,
                'error': str(e)
            }
    
    def _generate_final_report(self):
        """生成最终报告"""
        print(f"\n📊 生成最终验证报告...")
        
        # 计算总体状态
        architecture_ok = all(
            result.get('compliant', False) 
            for result in self.verification_results['architecture_verification'].values()
        )
        
        redundancy_ok = (
            self.verification_results['redundancy_check']['no_duplicates'] and
            self.verification_results['redundancy_check']['redundant_tables_removed'] and
            self.verification_results['redundancy_check']['tables_correctly_migrated']
        )
        
        integrity_ok = self.verification_results['data_integrity_check']['all_passed']
        
        overall_status = 'SUCCESS' if (architecture_ok and redundancy_ok and integrity_ok) else 'ISSUES_FOUND'
        self.verification_results['overall_status'] = overall_status
        
        # 保存报告
        report_file = f"final_architecture_verification_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.verification_results, f, ensure_ascii=False, indent=2)
        
        print(f"  📄 验证报告已保存: {report_file}")
        
        # 打印最终结果
        print(f"\n📋 最终验证结果:")
        print(f"  - 架构合规性: {'✅' if architecture_ok else '❌'}")
        print(f"  - 冗余消除: {'✅' if redundancy_ok else '❌'}")
        print(f"  - 数据完整性: {'✅' if integrity_ok else '❌'}")
        print(f"  - 总体状态: {'✅ 成功' if overall_status == 'SUCCESS' else '❌ 发现问题'}")
        
        # 打印架构摘要
        resource_info = self.verification_results['architecture_verification']['resource_db']
        business_info = self.verification_results['architecture_verification']['business_db']
        
        print(f"\n📊 最终架构摘要:")
        print(f"  - resource.db: {resource_info.get('table_count', 0)} 个表")
        print(f"  - fin_data.db: {business_info.get('table_count', 0)} 个表")
        print(f"  - 总表数: {resource_info.get('table_count', 0) + business_info.get('table_count', 0)}")

def main():
    """主函数"""
    print("🔍 最终架构验证工具")
    print(f"📅 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    verifier = FinalArchitectureVerifier()
    success = verifier.verify_architecture()
    
    if success:
        if verifier.verification_results['overall_status'] == 'SUCCESS':
            print(f"\n🎉 架构验证完全成功！")
            print(f"💡 提示: 数据库架构优化已完美完成")
        else:
            print(f"\n⚠️ 架构验证发现问题，请检查报告")
    else:
        print(f"\n❌ 验证过程中出现错误")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
