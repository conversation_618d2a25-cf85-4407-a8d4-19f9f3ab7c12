#!/usr/bin/env python3
"""
冗余表清理脚本
基于分析结果安全地删除冗余表并优化数据库架构
"""

import sqlite3
import shutil
import json
from datetime import datetime
from pathlib import Path

class RedundantTableCleaner:
    """冗余表清理器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.business_db = "fin_data.db"
        self.resource_db = "resource.db"
        
        # 基于分析结果的清理计划
        self.cleanup_plan = {
            'phase1_safe_removals': [
                'biz_business_rules',
                'biz_column_descriptions', 
                'biz_table_descriptions'
            ],
            'phase2_relocations': [
                'ai_prompt_templates',
                'query_patterns'
            ],
            'keep_tables': [
                'field_relationships',  # 与业务数据相关
                'data_quality_rules'    # 与业务数据相关
            ]
        }
        
        self.cleanup_log = {
            'timestamp': self.timestamp,
            'operations': [],
            'errors': [],
            'backup_info': {}
        }
    
    def cleanup_tables(self):
        """执行表清理"""
        print("🧹 开始冗余表清理...")
        print("=" * 60)
        
        try:
            # 1. 创建备份
            self._create_backup()
            
            # 2. 验证数据一致性
            self._verify_data_consistency()
            
            # 3. 第一阶段：删除冗余表
            self._phase1_remove_redundant_tables()
            
            # 4. 第二阶段：迁移系统元数据表
            self._phase2_relocate_system_tables()
            
            # 5. 验证清理结果
            self._verify_cleanup_results()
            
            # 6. 生成清理报告
            self._generate_cleanup_report()
            
            print(f"\n✅ 冗余表清理完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 清理过程中出现错误: {e}")
            self.cleanup_log['errors'].append({
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    def _create_backup(self):
        """创建备份"""
        print(f"\n📁 创建清理前备份...")
        
        backup_dir = Path("database_backups") / f"before_cleanup_{self.timestamp}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份fin_data.db
        if Path(self.business_db).exists():
            backup_file = backup_dir / f"{self.business_db}.backup"
            shutil.copy2(self.business_db, backup_file)
            print(f"  ✅ 备份: {self.business_db} → {backup_file}")
        
        # 备份resource.db（用于迁移操作）
        if Path(self.resource_db).exists():
            backup_file = backup_dir / f"{self.resource_db}.backup"
            shutil.copy2(self.resource_db, backup_file)
            print(f"  ✅ 备份: {self.resource_db} → {backup_file}")
        
        self.cleanup_log['backup_info'] = {
            'backup_path': str(backup_dir),
            'backup_time': datetime.now().isoformat()
        }
    
    def _verify_data_consistency(self):
        """验证数据一致性"""
        print(f"\n🔍 验证数据一致性...")
        
        # 验证配对表的数据是否真的一致
        pairs_to_check = [
            ('meta_business_rules', 'biz_business_rules'),
            ('meta_column_descriptions', 'biz_column_descriptions'),
            ('meta_table_descriptions', 'biz_table_descriptions')
        ]
        
        for resource_table, business_table in pairs_to_check:
            consistency = self._check_table_consistency(resource_table, business_table)
            
            if consistency['consistent']:
                print(f"  ✅ {business_table}: 数据一致，可安全删除")
            else:
                print(f"  ⚠️ {business_table}: 数据不一致，需要手动检查")
                self.cleanup_log['errors'].append({
                    'error': f'数据不一致: {business_table}',
                    'details': consistency,
                    'timestamp': datetime.now().isoformat()
                })
    
    def _check_table_consistency(self, resource_table, business_table):
        """检查两个表的数据一致性"""
        try:
            # 连接数据库
            resource_conn = sqlite3.connect(self.resource_db)
            business_conn = sqlite3.connect(self.business_db)
            
            resource_cursor = resource_conn.cursor()
            business_cursor = business_conn.cursor()
            
            # 获取记录数
            resource_cursor.execute(f"SELECT COUNT(*) FROM {resource_table}")
            resource_count = resource_cursor.fetchone()[0]
            
            business_cursor.execute(f"SELECT COUNT(*) FROM {business_table}")
            business_count = business_cursor.fetchone()[0]
            
            # 简单的一致性检查
            consistent = resource_count == business_count
            
            resource_conn.close()
            business_conn.close()
            
            return {
                'consistent': consistent,
                'resource_count': resource_count,
                'business_count': business_count
            }
            
        except Exception as e:
            return {
                'consistent': False,
                'error': str(e)
            }
    
    def _phase1_remove_redundant_tables(self):
        """第一阶段：删除冗余表"""
        print(f"\n🗑️ 第一阶段：删除冗余表...")
        
        conn = sqlite3.connect(self.business_db)
        cursor = conn.cursor()
        
        for table_name in self.cleanup_plan['phase1_safe_removals']:
            try:
                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                if cursor.fetchone():
                    # 删除表
                    cursor.execute(f"DROP TABLE {table_name}")
                    print(f"  ✅ 删除表: {table_name}")
                    
                    self.cleanup_log['operations'].append({
                        'operation': 'DROP_TABLE',
                        'table': table_name,
                        'database': self.business_db,
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    print(f"  ⚠️ 表不存在: {table_name}")
            
            except Exception as e:
                error_msg = f"删除表失败 {table_name}: {e}"
                print(f"  ❌ {error_msg}")
                self.cleanup_log['errors'].append({
                    'error': error_msg,
                    'timestamp': datetime.now().isoformat()
                })
        
        # 提交更改
        conn.commit()
        conn.close()
    
    def _phase2_relocate_system_tables(self):
        """第二阶段：迁移系统元数据表"""
        print(f"\n📦 第二阶段：迁移系统元数据表...")
        
        for table_name in self.cleanup_plan['phase2_relocations']:
            try:
                success = self._relocate_table(table_name, self.business_db, self.resource_db)
                if success:
                    print(f"  ✅ 迁移表: {table_name} → resource.db")
                else:
                    print(f"  ❌ 迁移失败: {table_name}")
            
            except Exception as e:
                error_msg = f"迁移表失败 {table_name}: {e}"
                print(f"  ❌ {error_msg}")
                self.cleanup_log['errors'].append({
                    'error': error_msg,
                    'timestamp': datetime.now().isoformat()
                })
    
    def _relocate_table(self, table_name, source_db, target_db):
        """迁移表到另一个数据库"""
        try:
            # 连接源数据库和目标数据库
            source_conn = sqlite3.connect(source_db)
            target_conn = sqlite3.connect(target_db)
            
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            # 检查源表是否存在
            source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not source_cursor.fetchone():
                print(f"    ⚠️ 源表不存在: {table_name}")
                return False
            
            # 检查目标表是否已存在
            target_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if target_cursor.fetchone():
                print(f"    ⚠️ 目标表已存在: {table_name}")
                return False
            
            # 获取表结构
            source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            create_sql = source_cursor.fetchone()[0]
            
            # 在目标数据库中创建表
            target_cursor.execute(create_sql)
            
            # 复制数据
            source_cursor.execute(f"SELECT * FROM {table_name}")
            rows = source_cursor.fetchall()
            
            if rows:
                # 获取列数
                source_cursor.execute(f"PRAGMA table_info({table_name})")
                columns = source_cursor.fetchall()
                column_count = len(columns)
                
                # 插入数据
                placeholders = ','.join(['?' for _ in range(column_count)])
                target_cursor.executemany(f"INSERT INTO {table_name} VALUES ({placeholders})", rows)
                
                print(f"    📊 复制了 {len(rows)} 条记录")
            
            # 提交目标数据库更改
            target_conn.commit()
            
            # 删除源表
            source_cursor.execute(f"DROP TABLE {table_name}")
            source_conn.commit()
            
            # 记录操作
            self.cleanup_log['operations'].append({
                'operation': 'RELOCATE_TABLE',
                'table': table_name,
                'source_db': source_db,
                'target_db': target_db,
                'records_moved': len(rows) if rows else 0,
                'timestamp': datetime.now().isoformat()
            })
            
            source_conn.close()
            target_conn.close()
            
            return True
            
        except Exception as e:
            print(f"    ❌ 迁移过程出错: {e}")
            return False
    
    def _verify_cleanup_results(self):
        """验证清理结果"""
        print(f"\n🔍 验证清理结果...")
        
        # 检查fin_data.db中的表
        conn = sqlite3.connect(self.business_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        remaining_tables = [row[0] for row in cursor.fetchall()]
        
        print(f"  📊 fin_data.db剩余表数: {len(remaining_tables)}")
        
        # 检查是否还有冗余表
        redundant_tables = [t for t in self.cleanup_plan['phase1_safe_removals'] if t in remaining_tables]
        if redundant_tables:
            print(f"  ⚠️ 仍有冗余表: {redundant_tables}")
        else:
            print(f"  ✅ 所有冗余表已删除")
        
        # 检查应保留的表
        expected_tables = ['financial_data', 'field_relationships', 'data_quality_rules']
        missing_tables = [t for t in expected_tables if t not in remaining_tables]
        if missing_tables:
            print(f"  ⚠️ 缺少重要表: {missing_tables}")
        else:
            print(f"  ✅ 重要表都已保留")
        
        conn.close()
        
        # 检查resource.db中的迁移表
        conn = sqlite3.connect(self.resource_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        resource_tables = [row[0] for row in cursor.fetchall()]
        
        migrated_tables = [t for t in self.cleanup_plan['phase2_relocations'] if t in resource_tables]
        print(f"  📊 已迁移到resource.db的表: {len(migrated_tables)} 个")
        
        conn.close()
    
    def _generate_cleanup_report(self):
        """生成清理报告"""
        report_file = f"table_cleanup_report_{self.timestamp}.json"
        
        # 添加汇总信息
        self.cleanup_log['summary'] = {
            'total_operations': len(self.cleanup_log['operations']),
            'tables_removed': len([op for op in self.cleanup_log['operations'] if op['operation'] == 'DROP_TABLE']),
            'tables_relocated': len([op for op in self.cleanup_log['operations'] if op['operation'] == 'RELOCATE_TABLE']),
            'total_errors': len(self.cleanup_log['errors']),
            'cleanup_completed_at': datetime.now().isoformat()
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.cleanup_log, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 清理报告已保存: {report_file}")
        
        # 打印汇总
        summary = self.cleanup_log['summary']
        print(f"\n📊 清理汇总:")
        print(f"  - 执行操作数: {summary['total_operations']}")
        print(f"  - 删除表数: {summary['tables_removed']}")
        print(f"  - 迁移表数: {summary['tables_relocated']}")
        print(f"  - 错误数: {summary['total_errors']}")
        
        if summary['total_errors'] == 0:
            print(f"  ✅ 清理完全成功！")
        else:
            print(f"  ⚠️ 有 {summary['total_errors']} 个错误，请检查日志")

def main():
    """主函数"""
    print("🧹 冗余表清理工具")
    print(f"📅 清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确认操作
    print("\n⚠️ 警告: 此操作将删除fin_data.db中的冗余表！")
    print("📋 将要删除的表:")
    print("  - biz_business_rules (与meta_business_rules 100%重复)")
    print("  - biz_column_descriptions (与meta_column_descriptions 100%重复)")
    print("  - biz_table_descriptions (与meta_table_descriptions 100%重复)")
    print("\n📋 将要迁移的表:")
    print("  - ai_prompt_templates → resource.db")
    print("  - query_patterns → resource.db")
    
    confirm = input("\n是否确认执行清理？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("❌ 清理操作已取消")
        return 0
    
    cleaner = RedundantTableCleaner()
    success = cleaner.cleanup_tables()
    
    if success:
        print(f"\n🎉 冗余表清理成功完成！")
        print(f"💡 提示: 数据库架构已进一步优化")
    else:
        print(f"\n❌ 清理过程中出现错误，请检查日志")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
