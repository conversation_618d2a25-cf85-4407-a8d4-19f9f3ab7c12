#!/usr/bin/env python3
"""
测试Redis初始化的独立脚本
"""
import asyncio
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_redis_initialization():
    """测试Redis初始化"""
    logger.info("开始测试Redis初始化...")
    
    try:
        # 测试配置加载
        logger.info("1. 测试配置加载...")
        from app.core.config import settings
        logger.info(f"Redis配置: ENABLED={getattr(settings, 'REDIS_ENABLED', 'NOT_SET')}")
        logger.info(f"Redis主机: {getattr(settings, 'REDIS_HOST', 'NOT_SET')}")
        logger.info(f"Redis端口: {getattr(settings, 'REDIS_PORT', 'NOT_SET')}")
        
        # 测试Redis模块导入
        logger.info("2. 测试Redis模块导入...")
        try:
            import redis.asyncio as redis
            logger.info("✅ Redis模块导入成功")
        except ImportError as e:
            logger.error(f"❌ Redis模块导入失败: {e}")
            return False
        
        # 测试Redis服务初始化
        logger.info("3. 测试Redis服务初始化...")
        from app.services.redis_cache_service import redis_cache
        await redis_cache.initialize()
        
        if redis_cache._initialized:
            logger.info("✅ Redis缓存服务初始化成功")
            
            # 测试基本操作
            logger.info("4. 测试Redis基本操作...")
            await redis_cache.set("test_key", "test_value", ttl=60)
            value = await redis_cache.get("test_key")
            if value == "test_value":
                logger.info("✅ Redis读写测试成功")
            else:
                logger.warning(f"⚠️ Redis读写测试失败: 期望'test_value'，实际'{value}'")
        else:
            logger.info("ℹ️ Redis缓存服务未初始化（可能被禁用或连接失败）")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Redis初始化测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return False

async def test_startup_sequence():
    """测试完整的启动序列"""
    logger.info("开始测试完整启动序列...")
    
    try:
        from app.core.startup import startup_sequence
        success = await startup_sequence()
        
        if success:
            logger.info("✅ 启动序列测试成功")
        else:
            logger.warning("⚠️ 启动序列测试部分失败")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 启动序列测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 开始Redis和启动诊断测试...")
    
    # 测试Redis初始化
    redis_success = await test_redis_initialization()
    
    print("\n" + "="*50)
    
    # 测试完整启动序列
    startup_success = await test_startup_sequence()
    
    print("\n" + "="*50)
    logger.info("📊 测试结果总结:")
    logger.info(f"Redis初始化: {'✅ 成功' if redis_success else '❌ 失败'}")
    logger.info(f"启动序列: {'✅ 成功' if startup_success else '❌ 失败'}")
    
    if redis_success and startup_success:
        logger.info("🎉 所有测试通过！服务器应该能正常启动。")
    else:
        logger.warning("⚠️ 部分测试失败，请检查上述错误信息。")

if __name__ == "__main__":
    asyncio.run(main())
