{"timestamp": "20250730_081438", "redundancy_analysis": {"business_rules": {"success": true, "resource_count": 17, "business_count": 17, "resource_schema": ["id", "table_name", "rule_category", "rule_description", "sql_example", "importance_level", "created_at"], "business_schema": ["id", "table_name", "rule_category", "rule_description", "sql_example", "importance_level", "created_at"], "schema_match": true, "overlap_count": 1, "overlap_percentage": 100.0, "redundancy_level": "HIGH"}, "column_descriptions": {"success": true, "resource_count": 31, "business_count": 31, "resource_schema": ["table_name", "column_name", "chinese_name", "description", "data_type", "business_rules", "ai_understanding_points", "field_category", "usage_scenarios", "common_values", "related_fields", "calculation_rules", "ai_prompt_hints"], "business_schema": ["table_name", "column_name", "chinese_name", "description", "data_type", "business_rules", "ai_understanding_points", "field_category", "usage_scenarios", "common_values", "related_fields", "calculation_rules", "ai_prompt_hints"], "schema_match": true, "overlap_count": 1, "overlap_percentage": 100.0, "redundancy_level": "HIGH"}, "table_descriptions": {"success": true, "resource_count": 1, "business_count": 1, "resource_schema": ["table_name", "description", "business_purpose", "data_scale", "created_at"], "business_schema": ["table_name", "description", "business_purpose", "data_scale", "created_at"], "schema_match": true, "overlap_count": 1, "overlap_percentage": 100.0, "redundancy_level": "HIGH"}}, "usage_analysis": {"ai_prompt_templates": {"exists": true, "record_count": 6, "column_count": 8, "columns": ["id", "template_name", "template_type", "template_content", "usage_scenario", "priority_level", "is_active", "created_at"], "purpose_assessment": "SYSTEM_METADATA - AI提示模板，应统一管理"}, "field_relationships": {"exists": true, "record_count": 7, "column_count": 8, "columns": ["id", "table_name", "primary_field", "related_field", "relationship_type", "relationship_description", "usage_example", "created_at"], "purpose_assessment": "UNCLEAR - 需要进一步分析"}, "query_patterns": {"exists": true, "record_count": 5, "column_count": 9, "columns": ["id", "pattern_name", "pattern_description", "natural_language_examples", "sql_template", "required_fields", "business_scenario", "difficulty_level", "created_at"], "purpose_assessment": "SYSTEM_METADATA - 查询模式，应统一管理"}, "data_quality_rules": {"exists": true, "record_count": 6, "column_count": 9, "columns": ["id", "table_name", "field_name", "rule_type", "rule_description", "validation_sql", "error_message", "severity_level", "created_at"], "purpose_assessment": "BUSINESS_METADATA - 数据质量规则，与业务数据相关"}}, "recommendations": [{"type": "REMOVE_REDUNDANT_TABLE", "priority": "HIGH", "table": "biz_business_rules", "reason": "与meta_business_rules高度重复(100.0%)", "action": "删除fin_data.db中的biz_business_rules表"}, {"type": "REMOVE_REDUNDANT_TABLE", "priority": "HIGH", "table": "biz_column_descriptions", "reason": "与meta_column_descriptions高度重复(100.0%)", "action": "删除fin_data.db中的biz_column_descriptions表"}, {"type": "REMOVE_REDUNDANT_TABLE", "priority": "HIGH", "table": "biz_table_descriptions", "reason": "与meta_table_descriptions高度重复(100.0%)", "action": "删除fin_data.db中的biz_table_descriptions表"}, {"type": "RELOCATE_TABLE", "priority": "LOW", "table": "ai_prompt_templates", "reason": "属于系统元数据，应统一管理", "action": "考虑将ai_prompt_templates迁移到resource.db"}, {"type": "RELOCATE_TABLE", "priority": "LOW", "table": "query_patterns", "reason": "属于系统元数据，应统一管理", "action": "考虑将query_patterns迁移到resource.db"}], "cleanup_plan": {"phase1_safe_removals": [{"type": "REMOVE_REDUNDANT_TABLE", "priority": "HIGH", "table": "biz_business_rules", "reason": "与meta_business_rules高度重复(100.0%)", "action": "删除fin_data.db中的biz_business_rules表"}, {"type": "REMOVE_REDUNDANT_TABLE", "priority": "HIGH", "table": "biz_column_descriptions", "reason": "与meta_column_descriptions高度重复(100.0%)", "action": "删除fin_data.db中的biz_column_descriptions表"}, {"type": "REMOVE_REDUNDANT_TABLE", "priority": "HIGH", "table": "biz_table_descriptions", "reason": "与meta_table_descriptions高度重复(100.0%)", "action": "删除fin_data.db中的biz_table_descriptions表"}], "phase2_conditional_removals": [], "phase3_relocations": [{"type": "RELOCATE_TABLE", "priority": "LOW", "table": "ai_prompt_templates", "reason": "属于系统元数据，应统一管理", "action": "考虑将ai_prompt_templates迁移到resource.db"}, {"type": "RELOCATE_TABLE", "priority": "LOW", "table": "query_patterns", "reason": "属于系统元数据，应统一管理", "action": "考虑将query_patterns迁移到resource.db"}], "backup_required": true, "testing_required": true}, "code_usage": {"biz_business_rules": {"used_in_code": false, "usage_context": "fin_data.db业务规则查询"}, "biz_column_descriptions": {"used_in_code": false, "usage_context": "fin_data.db字段描述查询"}, "biz_table_descriptions": {"used_in_code": false, "usage_context": "fin_data.db表描述查询"}, "ai_prompt_templates": {"used_in_code": true, "usage_context": "AI提示模板管理"}}, "summary": {"total_recommendations": 5, "high_priority_actions": 3, "potential_table_removals": 3, "analysis_completed_at": "2025-07-30T08:14:38.374357"}}